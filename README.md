# 📚 Simslab

**Simslab** is a reusable and extensible learning platform built with **Next.js** with tailwind Installed .  
It lets you create and deliver interactive courses for any subject — **Math**, **Science**, **HTML**, **AI**, and more.

---

## ✨ Features

- 📁 **Courses Folder**: Add new courses easily by adding a JSON file (e.g., `css.json`) inside the `courses` folder.
- 📑 **Chapters & Concepts**: Each course JSON defines chapters, concepts, and questions.
- 🧩 **Simulations**: Add optional interactive simulations for concepts to help learners understand topics visually and practically.
- 📖 **eBooks**: Each course can include an `ebook` folder with an `index.html` to load as reading material.
- ♻️ **Reusable**: The same structure works for any subject — extend or update without touching the core app.
- ⚡ **Built with Next.js**: Fast, modern, and easy to maintain.

---



